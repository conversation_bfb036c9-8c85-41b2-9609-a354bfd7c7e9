import { InfoCard } from "@/components/dashboard/info-card";
import { <PERSON>Field } from "@/components/dashboard/data-field";
import {
  Plus,
  FileText,
  ListFilter,
  Users,
  Building2,
  ClipboardList,
  Info,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Link } from "react-router-dom";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { StatCard } from "@/components/dashboard/stat-card";

export function DepartmentDetails({ selectedDepartment, isLoading }) {
  if (!selectedDepartment) {
    return (
      <div className="flex flex-col items-center justify-center md:h-[calc(100vh-4rem)] space-y-4">
        <p className="text-muted-foreground">
          {isLoading ? "Loading departments..." : "No department selected"}
        </p>
        {!isLoading && (
          <Button asChild>
            <Link to="/dashboard/academics/departments/create">
              Create a new department
            </Link>
          </Button>
        )}
      </div>
    );
  }

  return (
    <>
      <header className="border-b bg-background">
        <div className="container flex flex-col sm:flex-row h-auto sm:h-16 items-start sm:items-center justify-between p-4">
          <div className="w-full sm:w-auto mb-4 sm:mb-0">
            <h1 className="text-2xl font-semibold">
              {selectedDepartment.name}
            </h1>
            <Breadcrumb className="hidden sm:block lg:block md:hidden mt-1">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/academics/departments">
                    Departments
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{selectedDepartment.name}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <Button size="sm" asChild aria-label="Add new department">
            <Link to="/dashboard/academics/departments/create">
              <Plus className="h-4 w-4 mr-2" />
              Add Department
            </Link>
          </Button>
        </div>
      </header>
      <ScrollArea className="h-[calc(100vh-9rem)]">
        <main className="p-4 lg:p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6 mb-6">
            <StatCard
              title="Department"
              value={selectedDepartment.code}
              description={selectedDepartment.abbreviation}
              icon={Building2}
              loading={isLoading}
              valueClassName="uppercase"
              descriptionClassName="capitalize text-sm"
            />

            <StatCard
              title="Classification"
              value={selectedDepartment.type}
              description={`Status: ${selectedDepartment.status}`}
              icon={ListFilter}
              loading={isLoading}
              valueClassName="uppercase"
              descriptionClassName="capitalize"
            />

            <StatCard
              title="Established"
              value={
                selectedDepartment.establishedYear
                  ? new Date(selectedDepartment.establishedYear).getFullYear()
                  : "Not specified"
              }
              description="Year of Establishment"
              icon={Users}
              loading={isLoading}
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <InfoCard
              icon={Users}
              title="Department Head"
              isLoading={isLoading}
            >
              <div className="space-y-4">
                <DataField
                  label="Head of Department"
                  value={selectedDepartment.head || "Not assigned"}
                />
                <DataField
                  label="Academic Title"
                  value={selectedDepartment.headTitle || "Not specified"}
                  className="capitalize"
                />
                <DataField
                  label="Email"
                  value={selectedDepartment.headEmail || "Not specified"}
                  copyable
                />
                <DataField
                  label="Phone"
                  value={selectedDepartment.headPhone || "Not specified"}
                  copyable
                />
              </div>
            </InfoCard>

            <InfoCard
              icon={ClipboardList}
              title="Department Properties"
              isLoading={isLoading}
            >
              <div className="space-y-4">
                <DataField
                  label="Active Status"
                  value={selectedDepartment.isActive ? "Yes" : "No"}
                  badge={{
                    variant: selectedDepartment.isActive
                      ? "default"
                      : "secondary",
                  }}
                />
                <DataField
                  label="Has Courses"
                  value={selectedDepartment.hasCourse ? "Yes" : "No"}
                  badge={{
                    variant: selectedDepartment.hasCourse
                      ? "default"
                      : "secondary",
                  }}
                />
                <DataField
                  label="Has Laboratory"
                  value={selectedDepartment.hasLab ? "Yes" : "No"}
                  badge={{
                    variant: selectedDepartment.hasLab
                      ? "default"
                      : "secondary",
                  }}
                />
                <DataField
                  label="Current Status"
                  value={selectedDepartment.status}
                  className="capitalize"
                  badge={{
                    variant:
                      selectedDepartment.status === "active"
                        ? "default"
                        : "secondary",
                  }}
                />
              </div>
            </InfoCard>
          </div>

          {!isLoading && selectedDepartment.description && (
            <InfoCard
              icon={Info}
              title="Description"
              className="mt-6"
              isLoading={isLoading}
            >
              <p className="text-muted-foreground leading-relaxed">
                {selectedDepartment.description}
              </p>
            </InfoCard>
          )}
        </main>
      </ScrollArea>
    </>
  );
}
