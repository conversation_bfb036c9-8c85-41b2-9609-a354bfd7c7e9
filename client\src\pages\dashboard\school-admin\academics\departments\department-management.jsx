import { useState, useEffect } from "react";
import { Menu } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetDescription,
  SheetTrigger,
} from "@/components/ui/sheet";
import { DepartmentDetails } from "./department-list";
import { DepartmentDetails } from "./department-details";
import { useDepartment } from "@/context/department-context";
import { toast } from "sonner";

export default function DepartmentManagement() {
  const [departments, setDepartments] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const { fetchAllDepartments } = useDepartment();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const response = await fetchAllDepartments();
        setDepartments(response.data || []);
      } catch (error) {
        console.error("Error fetching departments:", error);
        toast.error("Error", {
          description: error.message || "Failed to fetch departments",
        });
        setDepartments([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const filteredDepartments =
    departments?.filter((dept) =>
      dept?.name?.toLowerCase().includes(searchQuery.toLowerCase())
    ) || [];

  const handleSelectDepartment = (dept) => {
    setSelectedDepartment(dept);
    setIsSheetOpen(false);
  };

  return (
    <div className="flex h-full w-full relative">
      {/* Mobile Sheet */}
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetTrigger asChild>
          <Button
            size="icon"
            className="md:hidden absolute top-6 right-4 z-50"
            aria-label="Open menu"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-80 p-4">
          <SheetHeader>
            <SheetTitle>Department Navigation</SheetTitle>
            <SheetDescription>Browse and manage departments</SheetDescription>
          </SheetHeader>
          <DepartmentList
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            departments={filteredDepartments}
            selectedDepartment={selectedDepartment}
            onSelect={handleSelectDepartment}
            isLoading={isLoading}
            isMobile
          />
        </SheetContent>
      </Sheet>

      {/* Sidebar (Desktop) */}
      <DepartmentList
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        departments={filteredDepartments}
        selectedDepartment={selectedDepartment}
        onSelect={handleSelectDepartment}
        isLoading={isLoading}
      />

      {/* Main Content */}
      <div className="flex-1 h-full overflow-hidden">
        <DepartmentDetails
          selectedDepartment={selectedDepartment}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
